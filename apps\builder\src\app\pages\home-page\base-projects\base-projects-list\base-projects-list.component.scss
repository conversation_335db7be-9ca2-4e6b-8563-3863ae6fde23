@use 'sass:map' as map;
@use '../../../../../styles/theme/gfk-light.palette.scss' as gfk;
@use '@gfk/style' as gfk-style;

.header-spacing {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.header-spacing-open-with-banner {
	margin-top: 131px;
}

.header-spacing-open-without-banner {
	margin-top: 54px;
}

.header-spacing-filter-area {
	margin-top: 56px;
}

.heading {
	border-top: 0px solid rgb(223 226 229);
	border-bottom: 1px solid rgb(223 226 229);
	padding: 10px 0px;
}

.grid-heading {
	display: flex;
	align-items: center;
	gap: 8px;
}

table {
	width: 100%;
}

table th {
	white-space: nowrap;
}

.selected-base-project-footer {
	
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: #ebecf0;
	padding: 20px 20px;
	position: fixed;
	width: 100%;
	bottom: 0px;
	z-index: 2;
	.m-0 {
		margin: 0px !important;
	}
	.ml-3 {
		margin-left: 12px !important;
	}
}

.transparent-background-Restore-btn {
  background-color: #ffffff !important;
  color: #379930 !important;
  padding: 6px 10px !important;
  border: 2px solid #379930 !important;
  border-radius: 5px !important;
  font-weight: 700 !important;
  .stroke-brand {
      stroke: #379930;
  }
  .fill-brand {
      fill: #379930;
  }
}

/* Add positioning for the flyout container */
.filter-area .toggle-button > div {
	position: relative; /* Set position to relative */
}

.tab-container {
	position: relative;
	z-index: 1;
}

.filter-area input[type='text'] {
	width: 420px;
}

.filter-area .input-container gfk-icon {
	position: absolute;
	top: 5px;
	right: 5px;
}

.filter-area .input-container button{
	z-index: 2;
}

.color-gfk-organge {
	color: gfk-style.$brand;
	cursor: pointer;
}

.inline-search-button {
	position: absolute;
	margin-left: 5px;
}

.filter-area input {
	width: 420px;
	height: 36px;
	padding: 0 12px;
}

.overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 100; /* Ensure the overlay is above the content */
}

.overlay-height-without-banner {
	height: 162vh !important;
}

.overlay-height-with-banner {
	height: 172vh !important;
}

.chips {
	position: relative;
    top: 5px;
    padding-top: 5px;
    padding-bottom: 5px;
    font-weight: 600;
}

.bp-conflict-detail-card {
	border: 1px solid #bbc0c9;
    border-radius: 5px;
    padding: 5px 10px;
	margin-bottom: 10px;
}

.bp-conflict-detail-height {
	max-height: 65vh;
    overflow-y: auto;
}

.bp-id-style:hover {
	color: gfk-style.$brand;
}

.transparent-background-delete-btn {
    background-color: #ffffff !important;
    color: #bc1c0e !important;
    padding: 6px 10px !important;
    border: 2px solid #bc1c0e !important;
    border-radius: 5px !important;
    font-weight: 700 !important;
    .stroke-brand {
        stroke: #bc1c0e;
    }
    .fill-brand {
        fill: #bc1c0e;
    }
}

.copied-bp-modal-content {
    max-height: 58vh;
    display: flex;
    flex-direction: column;
    overflow: hidden; 
}

.copied-bp-table-container {
    // flex: 1;
    overflow-y: auto;
    max-height: calc(60vh);
    min-height: 190px; 

	
    eds-table {
        height: 100%;

        thead {
            position: sticky;
            top: -10px;
            background-color: white;
            z-index: 2;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    }
}
