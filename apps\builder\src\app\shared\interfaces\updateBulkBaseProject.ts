export interface updateBulkBaseProject {
    baseProjectIds?: number[],
    dataTypeId?: number,
    purposeId?: number,
    suffix?: string,
    sqcMode?: number,
}


export enum SqcModeValues {
    INACTIVE = '0',
    ACTIVE = '1',
    SUPERVISED = '2'
}

export interface SqcModeOption {
    title: string;
    inputClass: string;
    name: string;
    value: string;
    hasError: boolean;
    checked: boolean;
    disabled: boolean;
}
  