import { TestBed } from '@angular/core/testing';
import { BaseProjectsListComponent } from './base-projects-list.component';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { DeleteApiService } from '@builder/shared/services/delete-api.service';

describe('BaseProjectsListComponent', () => {
  const getApiServiceMock = {
    getAsyncPanel: jest.fn().mockReturnValue(of([])), 
    getAsyncProjectSubType: jest.fn().mockReturnValue(of([])),
    getAsyncBaseProjectUsers: jest.fn().mockReturnValue(of([])),
    getCountries: jest.fn().mockReturnValue(of([])),
    getAsyncPeriodicities: jest.fn().mockReturnValue(of([])),
    getAsyncDataType: jest.fn().mockReturnValue(of([])),
    getAsyncPurpose: jest.fn().mockReturnValue(of([]))
  };

  const postApiServiceMock = {
    getAsyncProductGroup: jest.fn().mockReturnValue(of({})),
    getBaseProjectsListForFilter: jest.fn().mockReturnValue(of({}))
  };

  const deleteApiServiceMock = {
    deleteSelectedBaseProjects: jest.fn().mockReturnValue(of([]))
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [BaseProjectsListComponent],
      providers: [
        { provide: GetApiService, useValue: getApiServiceMock },
        { provide: PostApiService, useValue: postApiServiceMock },
        { provide: DeleteApiService, useValue: deleteApiServiceMock }
      ],
    }).compileComponents();
  });

  it('should create', () => {
    expect(BaseProjectsListComponent).toBeTruthy();
  });

});