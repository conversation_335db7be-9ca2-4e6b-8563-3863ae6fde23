/**
 * Interface for bulk edit form data
 */
export interface BulkEditFormData {
    baseProjectIds: number[];
    dataTypeId: number | null;
    purposeId: number | null;
    suffix: string | null;
    sqcMode: string | null;
    isRelevantForReporting: boolean | null;
}

/**
 * Interface for bulk QC project settings update payload
 */
export interface BulkQCProjectSettingsPayload {
    projectIds: number[];
    sqcMode: number;
}

/**
 * Enum for SQC Mode values
 */
export enum SqcModeValues {
    INACTIVE = '0',
    ACTIVE = '1',
    SUPERVISED = '2'
}

/**
 * Interface for SQC Mode radio button option
 */
export interface SqcModeOption {
    title: string;
    inputClass: string;
    name: string;
    value: string;
    hasError: boolean;
    checked: boolean;
    disabled: boolean;
}
