# Changelog
All notable changes to this project will be documented in this file. Dates are displayed in UTC.

## [2.32.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.32.1...2.32.2) (2025-09-09)

### Bug Fixes

* BX - Missing cases of RelevantForReporting ([d870f33](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/d870f33f78275977b6c1288f15bef224403919a6))
* Missing cases of RelevantForReporting ([d24a3d0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/d24a3d0012489a0f9a953f69c6b4080ccf4683a0))

## [2.32.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.32.0...2.32.1) (2025-09-08)

### Bug Fixes

* BX - Copy BP: PG not clearing when changing country ([1561d9a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/1561d9aca192e3fbe546a4d83cca9be291ce5955))
* Copy BP: PG not clearing when changing country ([0682504](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/0682504f74afb7de136f47b1f322a401228bfa4e))

## [2.32.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.31.0...2.32.0) (2025-08-20)

### Bug Fixes

* fixing test issues ([046aad0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/046aad05541ae3b1db7d46ae3985b85a9a0f4952))
* When we update suffix, and then again the modal box opens up. the text field is not cleared ([41b4e65](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/41b4e6566c921b5e678f2c6c9747f42e7e91d4f5))

### Features

* API integration for bulk BP edit ([fb7d360](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/fb7d360e1c7ad63bda576bf96035578b828b001c))
* Create UI for bulk BP edit ([dca8eb2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/dca8eb2c2772562e6684b1e8302c9467c1f78dde))
* edit bulk bp ([e13506e](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/e13506ec443a54f6330bff99c5a4ee56ac2515da))
* updated api url for edit bulk bp ([01b6c0a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/01b6c0a216718b2b58befb5d419a9d87d955a1b6))

## [2.31.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.30.0...2.31.0) (2025-07-27)

### Features

* update index.ts ([4c0142a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/4c0142a650f0dfb87fb55d39ad1f3d2e43183a4d))

## [2.30.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.29.0...2.30.0) (2025-07-27)

### Features

* updating @gfk/bff packages ([f122037](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/f122037cdb6d13f38fff96ca6ae2b74e55fd1250))
* updating bff package ([a16ef9f](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/a16ef9ffeec3115282ce9a00e332cf1bac530d67))

## [2.29.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.28.0...2.29.0) (2025-07-22)

### Features

* Show a message for 1000+ records ([cb13cc4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/cb13cc4402937c2b32b9b61ea019cb079b1d15a9))

## [2.28.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.27.1...2.28.0) (2025-07-22)

### Features

* Consumption of ProductGroup API ([07f9145](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/07f91450a925eb6fc0c9a3b6c42cb55f76ee129a))

## [2.27.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.27.0...2.27.1) (2025-07-22)

### Bug Fixes

* fixed lint issues ([c867dbb](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c867dbbedf6ca485896f235e747fd663ae0198d6))
* update urm api urls in bff ([2e2c37f](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2e2c37fba1bb6a8745d429228e1580b737397481))
* updated urm api urls ([3a33da9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3a33da960a1f090082603c48d040ae241ab366db))

## [2.27.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.26.0...2.27.0) (2025-07-21)

### Features

* indicate partial approval countries ([8cf0d5d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8cf0d5d88e3b6e606785b2c15d4ac6780b64bd4f))

## [2.26.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.25.0...2.26.0) (2025-07-21)

### Bug Fixes

* 100 limitation missing for bulk copy ([1656fbe](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/1656fbecf8e8a1b4fc28a699d60ef001824d7b30))
* Button position and textual changes ([9889228](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/988922897aba731fb4996767cd6e72bd01953d77))
* No option to copy new bp ids ([f73519b](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/f73519ba14072287b8d26f6dbb215205c7e13952))

### Features

* Enable Bulk Copy of Base Projects ([f0fec8a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/f0fec8a1cf903434efbd77ebe5dde640fb66792c))
* fixed lint issues ([2d7d400](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2d7d4005329af69f85f1ae273e077c5e62c059fa))

## [2.25.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.24.1...2.25.0) (2025-07-18)

### Features

* cycode bug fixes ([b9e3e85](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/b9e3e851076a09696b2a31efcabd215542c1dd03))

## [2.24.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.24.0...2.24.1) (2025-06-25)

### Bug Fixes

* DEK-8181 timezone ([95156c9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/95156c98f46aff293a67b056203498614c32386c))

## [2.24.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.23.4...2.24.0) (2025-06-25)

### Bug Fixes

* blank screen issue fixed ([01ce784](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/01ce7842e35248b7ea9e6a811a30627fef6b787b))
* BP getting created with ME and Daily combination ([97d39d6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/97d39d63984847eab97e845d6bcbb89be34cd836))
* bp list footer button issue ([93e4e7b](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/93e4e7bf021cf5c2819ad3001674ef4a3a65d8e5))
* finalize changes ([4753036](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/4753036651a61f7cce8f1dfa499d742fc671ddd1))
* Missing State Management in User Role API Causes No Results on Filtered Pagination ([0b2642a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/0b2642a502a251240cb0fb4535e80991cebdac50))
* Missing State Management in User Role API Causes No Results on Filtered Pagination ([5723167](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/5723167cbc65ff4c22fb5fc018ac35a795374aa4))
* modal not opening on repeat ([0ade877](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/0ade87769a6bb94c8e3b76a60d17b449dfa4ac19))
* unsaved changes issue ([dab3a1a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/dab3a1a0b6378165c07b7ae9b716b4ca46af6160))
* updated ([b042b75](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/b042b755a138e311a49d7cf6b32cd647e974e6c0))
* updated restriction for restore ([f87b174](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/f87b1744f6c1217aa55894e4f875e25f89285250))
* updated role filter array in bff ([268eff2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/268eff26b851680558ad792a897f34485aa815ba))

### Features

* BPAssociations ([048cf88](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/048cf88f266ac67cf4ab2cef7ece1ac68617ef85))
* bpAssociations label for select ([34e59c5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/34e59c5852faa1cfce8474b8d950506a0706a76b))
* countryfilter in settings ([087a789](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/087a789dfb7c3cf998f13311b7b84d3e7ed5357f))
* countryfilter in settings ([6215fdb](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/6215fdbde1557c8f96a7164b36e2cead8df59639))

## [2.23.4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.23.3...2.23.4) (2025-06-24)

### Bug Fixes

* blank screen issue fixed ([8ff888b](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8ff888bd1cd8766725a074bbc096526104ef7fe7))
* updated role filter array in bff ([282f2ad](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/282f2add7c0bb5dec56184c22d7a8bf098c713ee))

## [2.23.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.23.2...2.23.3) (2025-06-24)

### Bug Fixes

* bp list footer button issue ([a48fe3b](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/a48fe3b4fe28e4aa967b173e3f0a597b7aaee9d1))

## [2.23.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.23.1...2.23.2) (2025-06-20)

### Bug Fixes

* Missing State Management in User Role API Causes No Results on Filtered Pagination ([fe1f893](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/fe1f89324159f62c8bdcc031f4456fdfb16c84ab))

## [2.23.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.23.0...2.23.1) (2025-06-17)

### Bug Fixes

* BP getting created with ME and Daily combination ([756daea](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/756daeaaf407f03f5554b712d013f3feb58e58c8))

## [2.23.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.10...2.23.0) (2025-06-17)

### Bug Fixes

* resetonsave ([cd03152](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/cd031523ab5ad91f83f6edfb5dff0e4ce9bff17c))

### Features

* countryfilter in settings ([9fa997a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/9fa997a3be4d416cd1a26f40aa97daa6b93dc18e))

## [2.22.10](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.9...2.22.10) (2025-06-16)

### Bug Fixes

* modal not opening on repeat ([7d45459](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/7d454593bf230f486c636b4448041009cdcec9ff))
* updated restriction for restore ([45aa19b](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/45aa19bed2b9f79673babf1e7f76ebaf4c71595d))

## [2.22.9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.8...2.22.9) (2025-06-04)

### Bug Fixes

* resetaftersave ([f1c3e0e](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/f1c3e0eb8479246369d44ddd01d2f5fe03faabd9))

## [2.22.8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.7...2.22.8) (2025-06-04)

### Bug Fixes

* bugs ([4b5ef11](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/4b5ef119f4ebbb787ce19cac29c60173b075a457))
* bugs fix ([9bad34a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/9bad34a1ce9aaa8a33f6aab0dc9092b1e35e57a9))
* Removed bugs from copy base and edit screens ([fa1dabc](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/fa1dabc652cf3d747588f6a6d31ef635e22b3e7d))
* reset to production bug ([bbf68c2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/bbf68c2a1c72cebdeb0fd35f95fb825eed8de920))
* sort, exclude and filter ([adf3ab0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/adf3ab048d4086dcacd1d8b588c88ec1f6d78f4f))
* Sorting, exclusion and auto select ([21b2361](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/21b236190076acc2b3703c6332667b52d906c014))

## [2.22.7](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.6...2.22.7) (2025-06-04)

### Bug Fixes

* datetime issue fix ([0144320](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/01443203f772cccb8d410c1ce51d02ad6f0a5c04))

## [2.22.6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.5...2.22.6) (2025-06-04)

### Bug Fixes

* final textual changes ([92d834d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/92d834ddb1a48015a7e13a56473d95fe39cf3db1))
* merge main ([e15e2a5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/e15e2a5b810128edacb3f33f671726d710827bfe))

## [2.22.5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.4...2.22.5) (2025-06-03)

### Bug Fixes

* textual changes ([2980227](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2980227d0affe7cf37cd37217795545f4f3018d4))

## [2.22.4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.3...2.22.4) (2025-06-02)

### Bug Fixes

* chiplabel ([eedd115](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/eedd115c5066672a09cc74b35c59e003195f4063))

## [2.22.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.2...2.22.3) (2025-06-02)

### Bug Fixes

* utes feedback ([17eaba1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/17eaba18e92186deb54f6cc94aa0824f10bd0922))

## [2.22.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.1...2.22.2) (2025-05-29)

### Bug Fixes

* Cancel should be renamed to reset, as it is performing the action of RESET not CANCEL ([329b42a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/329b42a79ea098145fcb3085247384a7be42f0ab))
* Cancel should be renamed to reset, as it is performing the action of RESET not CANCEL ([8ecc91b](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8ecc91befc0179929548ce90434039dd57aafacb))
* fixing lint issues ([5379cb6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/5379cb692af4238cf598b39d0705972c8063a4d1))
* mvp feedback points ([f0f9e86](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/f0f9e867a20fb15b6dfa4b9d55f3afbe3d5bd9ae))
* Reset button missing from Copy BP. ([4350984](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/435098402328d20bba2e55f68e31c42db6178400))

## [2.22.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.22.0...2.22.1) (2025-05-29)

### Bug Fixes

* Fixed peiodicity and save bp bug ([306f98f](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/306f98f897487e0f45ede3070a4588c4909f48c6))
* intervalnames ([54aea66](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/54aea669dd23041683d8dbca3f778ddb12941f27))

## [2.22.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.21.2...2.22.0) (2025-05-26)

### Features

* Notification update when user Approves/Declines/Revokes master ([b9d5126](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/b9d5126e0ac84e30d3beab7d9e99665283876950))

## [2.21.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.21.1...2.21.2) (2025-05-26)

### Bug Fixes

* bphistoryvalue add ([dce8bd3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/dce8bd39b6d19aa8ef67f5e78ece9d5f71d1f533))
* bphistoryvaluechange rem ([b16f8d2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/b16f8d281893c70726bc82e8ad97a292d0de9249))
* daterange state change ([19a29c9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/19a29c9964abf8d5b456932b09807b3b2be7d02d))
* qc ids and bp ids reset ([bd1f59f](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/bd1f59f83b67e87c7730183c1ac7c3e1dc1cb2b6))

## [2.21.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.21.0...2.21.1) (2025-05-25)

### Bug Fixes

* daterange ([ac064fe](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ac064fe223052baa676cffd9a2cd983c4045be4e))
* try ([4cfe536](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/4cfe536b2e79ef311fee9458248887c55936d691))
* undo ([aee4a66](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/aee4a66283b0ee16609600943a93c6553c43cccb))

## [2.21.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.20.0...2.21.0) (2025-05-25)

### Features

* Change PG to DPG in the filter ([afb7f74](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/afb7f7462d5189c447fd2b809c78aea24dae3683))
* Change PG to DPG in the filter ([c300610](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c300610c31000bf0900904fc92e6e70acc7024cd))

## [2.20.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.19.3...2.20.0) (2025-05-23)

### Bug Fixes

* removed button ([2390eab](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2390eab7c2f90d828854981efbf3faa1922ae4dd))
* umers code fix ([ea8d223](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ea8d22342dccb9538d8f5f4fd552005d50f12b03))

### Features

* cosmetics changes update ([63d6942](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/63d6942d9a8a93fcdf67b5f4f87200b2ad13d2a6))
* Restore Deleted Base Projects ([75c3b34](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/75c3b3401a4fcf4f54decea4fc68096990d4eeec))
* Restoring base projects ([cb80fdd](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/cb80fdda2087e79244c4995f4af82bbde31e0013))

## [2.19.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.19.2...2.19.3) (2025-05-22)

### Bug Fixes

* fixed lint issues ([9dd172e](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/9dd172e53d9b954d9b1b7a89f2247d2492ea8e02))
* MVP feedback points ([8e68a2c](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8e68a2c8282ed804d4e6f91284bec02b65189772))

## [2.19.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.19.1...2.19.2) (2025-05-22)

### Bug Fixes

* further ([3a1b824](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3a1b824c9754e418ee36e453ef2d7e55bdaad078))
* lint ([a67755f](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/a67755fe34f8e2a6923e7667fed7031f3aba45ce))
* pgload ([a6cfa25](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/a6cfa25fa52a07f61df4bdcd19dfc1440d18de48))

## [2.19.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.19.0...2.19.1) (2025-05-19)

### Bug Fixes

* fixed test cases ([42b19b7](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/42b19b78f57a2ab3d4a87834a8340e8c5ecee771))
* Periodicity not changing as per data type while copying Base Project ([f4f8b33](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/f4f8b33295d9f79b4b43a42ad697464c19a6a3da))

## [2.19.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.19...2.19.0) (2025-05-18)

### Features

* copy to clipboard button added ([c23f9cf](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c23f9cf20ff92297fc5302c72845ee6b7e5ff6cd))

## [2.18.19](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.18...2.18.19) (2025-05-16)

### Bug Fixes

* sonarqube fixes ([fafe0cc](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/fafe0cc1e28705ee614fc45a65ad3f31d186aa1e))

## [2.18.18](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.17...2.18.18) (2025-05-16)

### Bug Fixes

* BP security Loader keeps on showing ([6006c91](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/6006c914a0faf5a3b3bc9f47d3a8e3ac6c38dd8b))

## [2.18.17](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.16...2.18.17) (2025-05-14)

### Bug Fixes

* Update notification text ([2320b67](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2320b67097f6d3c5168c647bd256312e1e36558a))

## [2.18.16](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.15...2.18.16) (2025-05-12)

### Bug Fixes

* JIRA URL issue fixed ([0eeb563](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/0eeb563375fe7dc9c2bf688a36e901ff9eceecf2))

## [2.18.15](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.14...2.18.15) (2025-05-12)

### Bug Fixes

* mvp critical issues ([3c56025](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3c560257973412c752ea723a7db0cbcdbed4a464))

## [2.18.14](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.13...2.18.14) (2025-05-11)

### Bug Fixes

* mvp bugs fixed ([2a5e3be](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2a5e3be8d152ef3597762765085bdc7997dd7807))
* Rollout MVP to Production (on 13/5) ([bf40498](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/bf40498538457b25978dde3bc135eb4f104012cc))

## [2.18.13](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.12...2.18.13) (2025-05-07)

### Bug Fixes

* listing ([bb631e5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/bb631e567991696cc0897e4319071874c218de04))
* revert ([e1bbd57](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/e1bbd576444b65c7a3efea3ad86aedd018f78099))

## [2.18.12](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.11...2.18.12) (2025-05-06)

### Bug Fixes

* Retailer Separation radio button state management ([59cd262](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/59cd2625bf444dbb487e87d78b8905328e64e2d4))

## [2.18.11](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.10...2.18.11) (2025-05-06)

### Bug Fixes

* bug fix ([26072b5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/26072b592f55fe5b06cd862872718658a21c5e98))
* changes in middleware ([3f79e83](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3f79e83bf9646ad719b10c1d3f168737faae39ab))
* connectivity issue ([c3044b4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c3044b4b4331248086490f26e2eafb826b876585))
* fixed lint issues ([ab9dc42](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ab9dc4298394eba0aa1fa832abfab9bf9a9b6659))
* signalR connection issue fixed ([d52321c](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/d52321cb5e861cf6abe967eb59e39b23cadc799b))
* signalR issue ([dc637ef](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/dc637ef55a881b00ceeaa649703465b127c3e194))
* updated middleware ([fe12b54](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/fe12b547a0c9487d318903b9d2c3c3e8774098a9))
* updated signalR connection url ([da19224](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/da19224a5c8b30da6c9eb2cdf6523d55e3d9f340))

## [2.18.10](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.9...2.18.10) (2025-05-06)

### Bug Fixes

* signalR connectivity issue ([3eb8ea0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3eb8ea099c7e3021a81dbd8169b9954b1b764469))

## [2.18.9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.8...2.18.9) (2025-05-06)

### Bug Fixes

* loader bug fix ([bb84ce2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/bb84ce2d9c2f5f4ec72d40bbbcdec3170c86314d))
* Loader fixed ([68edfd6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/68edfd6fb0b608356f2ce8d03c5132017d332691))

## [2.18.8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.7...2.18.8) (2025-05-05)

### Bug Fixes

* pg changes fix ([3c2bfb4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3c2bfb4889cff9c876b7521c84e5a5be6cd9ea70))

## [2.18.7](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.6...2.18.7) (2025-05-05)

### Bug Fixes

* signalR connection issue ([c06df41](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c06df41e3ca6e71d574696501b945ceef08a6652))

## [2.18.6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.5...2.18.6) (2025-05-05)

### Bug Fixes

* signalR issue ([052928b](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/052928bd33ab0e56360f315d939a09c6dd53092b))

## [2.18.5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.4...2.18.5) (2025-05-05)

### Bug Fixes

* added other periodicities ([91d0008](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/91d000825b3917395624d1a1b565bf9dcb7a0a47))
* N/A bug fixed ([17777c9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/17777c9a59ae23070f2e80358b4c1718ed5f7bc3))
* testing signalR ([16e96c4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/16e96c42e1cbd4b6760981edc2e242595a42b858))

## [2.18.4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.3...2.18.4) (2025-05-02)

### Bug Fixes

* lint ([e5253fe](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/e5253fe1a8541dfaccef2768c47ad075733134a4))
* pg bug fixes ([ec68def](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ec68defeb28af46597a6a90bf2b174eef8e5e54b))

## [2.18.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.2...2.18.3) (2025-04-29)

### Bug Fixes

* pg remove alert ([e97d733](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/e97d733e6a365183d4c914985951fd6e3c46e881))

## [2.18.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.1...2.18.2) (2025-04-29)

### Bug Fixes

* BulkQcTableBug ([0f07451](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/0f0745121377bb060b1ade21e132e2e8a07207c7))

## [2.18.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.18.0...2.18.1) (2025-04-25)

### Bug Fixes

* Restrict Master user removal from BP Security List ([423932a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/423932a2a9ef254ef0453abdee3445df18a91431))

## [2.18.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.10...2.18.0) (2025-03-27)

### Bug Fixes

* review point ([1063e68](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/1063e689050074fed4c815def10b059a7028ad23))
* review point ([1d43f4e](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/1d43f4e23ea3fef8f5d538cea425cc64d98306b9))

### Features

* reference periodupdate against reference project ([8d252fc](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8d252fc15b0dc16570003de6798258a5c0b6f4ad))

## [2.17.10](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.9...2.17.10) (2025-03-26)

### Bug Fixes

* footer changes ([6195f42](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/6195f42873a9f6bca7f77279a1cc67ff06bf25b9))

## [2.17.9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.8...2.17.9) (2025-03-26)

### Bug Fixes

* PG Field not getting enabled when selecting Sector/Category ([b076029](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/b07602955cf0c07e335a73749554ec337414425f))
* solved issue ([e147759](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/e147759262b1bc13004f98c7e14e6818ef35acbb))

## [2.17.8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.7...2.17.8) (2025-03-26)

### Bug Fixes

* signalR connection issue fixed ([f8256d3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/f8256d3e9612005b305e361933d1fe8e494509bc))

## [2.17.7](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.6...2.17.7) (2025-03-26)

### Bug Fixes

* Incorrect notification flow from RS list ([bf5f47a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/bf5f47a8dc2d2700fdcabbc28220e5c00d45b58e))

## [2.17.6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.5...2.17.6) (2025-03-26)

### Bug Fixes

* BP Security search not working ([75d3523](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/75d3523f021982bafc21b346b212f9e8da568663))

## [2.17.5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.4...2.17.5) (2025-03-25)

### Bug Fixes

* chip color change ([574169e](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/574169eb2491ff5fbad0d44a3943b229f693ef3f))

## [2.17.4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.3...2.17.4) (2025-03-24)

### Bug Fixes

* signalR url issue fixed ([183b667](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/183b66761a991ca8ff32974723604975fdeacab5))

## [2.17.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.2...2.17.3) (2025-03-24)

### Bug Fixes

* SignalR implementation changes ([fe1ae4d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/fe1ae4d4adfaecfbb8196162394b2f16f2ab6969))
* signalR issue fixed ([b591d2c](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/b591d2cd795152243f1dee66eaa3674677b94564))
* SignalR localstorage implementation changes ([9b4d408](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/9b4d40871a8c9a4f862ede6a049d347fe9b2e8bb))

## [2.17.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.1...2.17.2) (2025-03-24)

### Bug Fixes

* Alpha banner adjustment ([9936f7f](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/9936f7f89429ac034a163d94c02b755a42f2e02d))
* removed comment ([7f61432](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/7f61432f780a5186bed206efbabe5115fd0609ce))
* Removed test case from alpha banner ([0a2b390](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/0a2b390b2eb61b7a327db2eb300b7442b33e4a33))

## [2.17.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.17.0...2.17.1) (2025-03-23)

### Bug Fixes

* ... ([a7b23bc](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/a7b23bc088db954f96ee649976ffbd188ceef4b0))
* mvp changes ([ed3b946](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ed3b94620ffae7a7f98ad6560c79349738268784))
* signalR changes ([3a9eae2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3a9eae27535571b9c1b8f66b95540044bb109ff3))

## [2.17.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.16.2...2.17.0) (2025-03-18)

### Features

* updating help and support url ([432f355](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/432f355d0edfdcc89308fb7e6014950f327568ff))
* updating test cases ([0c7ddbd](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/0c7ddbd2f8fcf1accd4791b8c8cb4f894ceefbe7))

## [2.16.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.16.1...2.16.2) (2025-03-18)

### Bug Fixes

* signalR proxy url issue fixed ([67efaf8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/67efaf84efc631b63bf326206e4a90dad3cc8228))

## [2.16.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.16.0...2.16.1) (2025-03-18)

### Bug Fixes

* adjusted footer ([1d95c52](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/1d95c52159d2440059ab165a93015df918325f50))

## [2.16.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.15.0...2.16.0) (2025-03-18)

### Features

* CSV download feature changes ([7cd829e](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/7cd829e0a1b6e3457b1e5ccf84fd20706a0facc3))

## [2.15.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.14.1...2.15.0) (2025-03-17)

### Features

* removed semvar for footer ([bd37f94](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/bd37f940a1339bdca07f06068ab568613269d382))

## [2.14.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.14.0...2.14.1) (2025-03-17)

### Bug Fixes

* notification issues fixed for signal R ([b5c6284](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/b5c628464539e75178b1140b8f12af825bc3bd79))
* signal r issue fixed ([405749d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/405749da9721a2eadead9b8e5c586b9731f9f43e))

## [2.14.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.13.2...2.14.0) (2025-03-17)

### Features

* signal R implementation ([d86baae](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/d86baae9762449b7ca7781b722293b23cf8fc0e2))
* Signal R implementation changes ([ce9cfbb](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ce9cfbb326c6ded2e65ba79e0a3e7d69a12ffc3d))
* Signal R Implmentation ([6560be7](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/6560be75fd84c25e75280b448450a6e530f93476))
* updated test cases ([c55dcc9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c55dcc9a24b4bc5724b8eecf2cbf111ddc82e16e))

## [2.13.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.13.1...2.13.2) (2025-03-14)

### Bug Fixes

* PG not clearing upon changing country ([45c9e83](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/45c9e83d81c6202ed1aa8884386bc27e68ff0ebc))

## [2.13.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.13.0...2.13.1) (2025-03-13)

### Bug Fixes

* DateType to be filtered for IDAS on Copy and Create ([3f883cf](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3f883cf85a8799821a5d587febd71907477506a6))

## [2.13.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.12.5...2.13.0) (2025-03-12)

### Features

* Rollout MVP to T5 env. ([8c06290](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8c062908ac2f83a9a9b61b64f478b339bf275b20))

## [2.12.5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.12.4...2.12.5) (2025-03-06)

### Bug Fixes

* filter data type dropdown list based on panel selection ([2861fa9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2861fa9b5bf6486a20c8ee1d9b25340f303edb51))

## [2.12.4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.12.3...2.12.4) (2025-03-06)

### Bug Fixes

* Unable to edit periodicity while copying BP ([416a3a3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/416a3a340d5591ecfbd5a1de8f514a4c19baf513))

## [2.12.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.12.2...2.12.3) (2025-03-05)

### Bug Fixes

* Results not showing if user on second page ([793d680](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/793d680b35136810b019ab4c069060a36fe0de87))

## [2.12.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.12.1...2.12.2) (2025-03-05)

### Bug Fixes

* DataType not respecting selected periodicity ([9d98059](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/9d980591bc12bcfd3bca7aec00862254a6bad6be))

## [2.12.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.12.0...2.12.1) (2025-03-04)

### Bug Fixes

* Update placeholder ([44cf07d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/44cf07d5e0f9c530c3878f971bd8e520c366605f))

## [2.12.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.11.1...2.12.0) (2025-03-04)

### Features

* Restrict Periodicity options based on Data Type selection ([a822a59](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/a822a598d6bef2d78c9a9820daa2dbe99a25958f))

## [2.11.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.11.0...2.11.1) (2025-03-02)

### Bug Fixes

* fixed lint issues ([b30c1c7](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/b30c1c7afc8b4e79e80cdab156af0ae7b8ba6707))
* Showing editable for unknown. ([3434bf9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3434bf959c5c168c9b28f970e6f1c3c0b8de1045))

## [2.11.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.10.6...2.11.0) (2025-02-26)

### Features

* Filters on internal user list in BP & QC Security ([7e0cff0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/7e0cff07dd3f6c3bc0f6838eabfa888d86b681e6))

## [2.10.6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.10.5...2.10.6) (2025-02-24)

### Bug Fixes

* DataType not respecting selected periodicity ([6fb15c9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/6fb15c972668be2f2277d01c5b11b89143700cea))

## [2.10.5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.10.4...2.10.5) (2025-02-20)

### Bug Fixes

* try again ([376d7b6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/376d7b6452b4096e82d715dad6106a0e50c4a396))
* try again ([042a937](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/042a937a412998be4d88e9fc3bbb925e8473a418))

## [2.10.4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.10.3...2.10.4) (2025-02-19)

### Bug Fixes

* DataType and Purpose to be only editable for Master level users ([6aced2d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/6aced2d789391ca7f7e8cd7de4d28db88e328fd8))

## [2.10.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.10.2...2.10.3) (2025-02-19)

### Bug Fixes

* approvecase ([361d99a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/361d99ae1986ce2715cd02c86b19bf2f3c63a61a))

## [2.10.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.10.1...2.10.2) (2025-02-18)

### Bug Fixes

* anotherbug ([3d497c4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3d497c47cb11b34610ee1b1f1b84393209e5a0ea))

## [2.10.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.10.0...2.10.1) (2025-02-18)

### Bug Fixes

* revokecountry bug fixes ([8e8d874](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8e8d8742f10e72d1acf036d52566227269b20144))

## [2.10.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.9.2...2.10.0) (2025-02-18)

### Features

* UI changes ([e927a99](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/e927a99126addacdc13075eb65a34fcd94751246))

## [2.9.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.9.1...2.9.2) (2025-02-18)

### Bug Fixes

* lint ([f388511](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/f3885116c553979a22ad3ebbef13252b4341c459))
* userrole lint ([dd7f150](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/dd7f1500b605fe10604a6635fcd06ae09b3e31dd))

## [2.9.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.9.0...2.9.1) (2025-02-18)

### Bug Fixes

* Loader missing from tabs when loading ([de056b5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/de056b5cc8ee826e78107e01defe843bf553837c))

## [2.9.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.8.0...2.9.0) (2025-02-17)

### Features

* Update notification on removal of users ([918665b](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/918665ba6b438552017c51b25f4fe94b636ca167))

## [2.8.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.7.3...2.8.0) (2025-02-17)

### Features

* DataType & Purpose to be editable for Master user ([76f416a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/76f416aaefbf2cf672d3d1bdb74e5824747c3e19))

## [2.7.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.7.2...2.7.3) (2025-02-14)

### Bug Fixes

* fixed lint issues ([3011825](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3011825e82c5f252477e387c4004c252efa4fccd))
* handle loader issues ([92561fa](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/92561faab2e82571f8eb75472c8e862f815a6fcd))
* Loader missing from tabs when loading ([d537739](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/d537739f9b67630893390cd62bf3ae2db2d98d6f))

## [2.7.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.7.1...2.7.2) (2025-02-13)

### Bug Fixes

* Button fix ([013035f](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/013035f91f31bb00ea71323c80af9d2a5ddf11ed))

## [2.7.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.7.0...2.7.1) (2025-02-12)

### Bug Fixes

* rolefilter ([55a3cce](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/55a3cced6f7b0bd5b14a1ccecbdc4d3dd3fc9451))

## [2.7.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.6.2...2.7.0) (2025-02-12)

### Features

* Change API URL from panel type to data type ([d3b6185](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/d3b6185ccdf99a2bcc3ac9eed253e008ac63cf2f))

## [2.6.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.6.1...2.6.2) (2025-02-11)

### Bug Fixes

* Fixed I/R button against user roles ([2d7f0d8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2d7f0d85ae4d35309af81ea80e700c225d783e29))

## [2.6.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.6.0...2.6.1) (2025-02-10)

### Bug Fixes

* userrole ([62c88d0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/62c88d0e8a3a2d70bde891f97cb0eb1698e099f4))
* userrole ([6c2a251](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/6c2a2516a436eab9c9f98eda0577eabbee624516))

## [2.6.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.5.1...2.6.0) (2025-02-10)

### Features

* unassign country ([c9913ac](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c9913acc2cf46dfe7f1c4588a312ecd8acadef7c))

## [2.5.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.5.0...2.5.1) (2025-02-09)

### Bug Fixes

* Retailer separation saving values to No ([4f179ac](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/4f179ace99c4883fc002f8d6b062683561e5ef75))

## [2.5.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.4.2...2.5.0) (2025-02-06)

### Features

* buttom added ([ee96dbf](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ee96dbf200af2d76767eec203de415e254dfdf25))

## [2.4.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.4.1...2.4.2) (2025-02-06)

### Bug Fixes

* removed Qc project data from BP list ([4530cd1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/4530cd14e84b14cb60d2830af5fdf38feb5466cc))

## [2.4.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.4.0...2.4.1) (2025-02-04)

### Bug Fixes

* removed PG from listing page ([1971ab0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/1971ab04ad38376f6bbd88d9e6425cca42da2587))

## [2.4.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.3.1...2.4.0) (2025-02-04)

### Features

* Handle api response for restricting master user delete in BP security ([a409e46](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/a409e4633b326751b75c57d454370c25fcaf84c6))

## [2.3.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.3.0...2.3.1) (2025-01-30)

### Bug Fixes

* reverted opa implementation from BX ([7dabeee](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/7dabeee51a7cd60d735bc4e2fe0166439a0dbf63))

## [2.3.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.2.8...2.3.0) (2025-01-30)

### Features

* Prevent API calls while switching tabs in BP Detail ([e40f24b](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/e40f24b719b4e6e7ae5c9e7338ca387a1d202652))

## [2.2.8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.2.7...2.2.8) (2025-01-29)

### Bug Fixes

* CSV format download ([8a846f1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8a846f15a6f7974ee3132f95a74c12b37979f7ab))

## [2.2.7](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.2.6...2.2.7) (2025-01-29)

### Bug Fixes

* User Role Assignment Issue ([edea29a](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/edea29a5e1410c2adab1b6077466a85daeac80ba))

## [2.2.6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.2.5...2.2.6) (2025-01-21)

### Bug Fixes

* ui fixes ([731bce0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/731bce049150d1d018860acf4b3698049f1c193c))

## [2.2.5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.2.4...2.2.5) (2025-01-20)

### Bug Fixes

* DEK-6626 ([a37b56e](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/a37b56e87669a7ab797e80e81d601ff49e12bd4a))
* updated test cases ([7cbbd6c](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/7cbbd6c022b2cfbf550f047f455fdb80f8427905))

## [2.2.4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.2.3...2.2.4) (2025-01-17)

### Bug Fixes

* CSV white space issue ([82f5488](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/82f5488fcb46accd349c754f83281832fd3db3ef))

## [2.2.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.2.2...2.2.3) (2025-01-16)

### Bug Fixes

* logs in bff ([94e6fca](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/94e6fca4d81e79e6d7b978371f6106ac62ca31a7))

## [2.2.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.2.1...2.2.2) (2025-01-15)

### Bug Fixes

* CSV file containing white spaces ([9bf4e96](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/9bf4e9636acc4be3919e7f26a99a23149f4207c8))

## [2.2.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.2.0...2.2.1) (2025-01-15)

### Bug Fixes

* bug - cosmetic and modal ([eba36e8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/eba36e806e385969dda9bb035421765ef7f2cbd9))

## [2.2.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.1.2...2.2.0) (2025-01-14)

### Features

* add Bulk BP security users ([05cd066](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/05cd066fe9277fa168f3695ecc3b6ece9909f808))

## [2.1.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.1.1...2.1.2) (2025-01-14)

### Bug Fixes

* I/R button and Test cases update ([19941ef](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/19941efcf78be42c426d77b6b279cd66fdef9fc1))

## [2.1.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.1.0...2.1.1) (2025-01-13)

### Bug Fixes

* Delete QC Period Bug fix ([c2941ca](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c2941cadd3504abee3aa6d7c2ef28168f63e9f14))

## [2.1.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.14...2.1.0) (2025-01-13)

### Bug Fixes

* CSV file format ([18abb7f](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/18abb7f686ab4d570fd817128f315a6d1a30a93d))

### Features

* Change csv filename format ([3279e65](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/3279e65d5052a2cbcd51bda47b12803d21d400ae))

## [2.0.14](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.13...2.0.14) (2025-01-11)

### Bug Fixes

* Rename Home Page anchor ([7281ab8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/7281ab8422bb90c563d320babed26ef4f4da578e))

## [2.0.13](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.12...2.0.13) (2025-01-09)

### Bug Fixes

* removed loader ([47d4768](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/47d4768b16f49e1dac7a80aabeca035681e70fc2))

## [2.0.12](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.11...2.0.12) (2025-01-08)

### Bug Fixes

* testing footer ([756be4d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/756be4d7e8b14568fc428d209479244c75a8ba01))
* Versoin test ([c8400a4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c8400a4f60576f58f557bfafeb7f9e6aaf087c3a))

## [2.0.11](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.10...2.0.11) (2025-01-08)

### Bug Fixes

* testing versioning ([23642c8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/23642c8192cfdf5597c7925e9540e9b2d45bc82a))

## [2.0.10](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.9...2.0.10) (2025-01-08)

### Bug Fixes

* hard reload test and loader for bpsecurity ([8aafc84](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8aafc845a7665f9889192bbea8a18276f70df5b6))
* test cases ([9782192](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/97821923cf401d561c2893b3b4e3f20f31e8580e))
* testing version update ([1d5531d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/1d5531d105a82ad3ce2a722679f28fe2a94351c0))
* updated test cases ([be24d91](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/be24d91163c7e77c647ea05fd98932cd0a49c7f6))

## [2.0.9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.8...2.0.9) (2025-01-07)

### Bug Fixes

* bpSecurity showing up in distributor type ([2be6ef7](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2be6ef7272d89c2e94e23f5594b3a6bb66ff0d2d))

## [2.0.8](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.7...2.0.8) (2025-01-07)

### Bug Fixes

* change button names ([8286448](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/828644854db1a0f58121caf48aeee8d4ad04a6d8))
* footer console removed ([ca120e9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ca120e98960faa8bb8111a7789d21a08d3e9f793))
* removed comments ([47ed56d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/47ed56d861f7d62c26c25f1123b98e07f3b011af))

## [2.0.7](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.6...2.0.7) (2025-01-07)

### Bug Fixes

* test and BP name ([8e05207](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/8e052079a04bb19e5b30be1f11d96c4154a2a4d8))

## [2.0.6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.5...2.0.6) (2025-01-06)

### Bug Fixes

* version bug ([a66c5fa](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/a66c5fa5e8b42e576bbfcd565e515dfa6f782fe8))

## [2.0.5](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.4...2.0.5) (2025-01-06)

### Bug Fixes

* adding components ([88b6b4e](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/88b6b4e8f2345a192384d68bdbae86194dc83595))
* display user list in dropdown fix ([9da3bac](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/9da3bac4a28e80c5c3d58788bf81ca3993f42cca))
* review points ([9d5b92d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/9d5b92dfa65d9afd9cf3750c7e3d52dec7d313e1))
* Review points ([76cfe44](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/76cfe44d94c80b8d8ef34405536ba95fa4a22605))
* test cases fixed ([c378b6e](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/c378b6eb73ce85076a5d2f973221e66cb6af94f2))

## [2.0.4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.3...2.0.4) (2025-01-06)

### Bug Fixes

* Testing ([6f6a155](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/6f6a15592a5e8e3cddd222cff2ea0e6cb66a12f9))

## [2.0.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.2...2.0.3) (2025-01-03)

### Bug Fixes

* test ([7803776](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/7803776f0103b72f7887e93e622e321774fb8b94))

## [2.0.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.1...2.0.2) (2025-01-03)

### Bug Fixes

* Testing ([0d20a16](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/0d20a16cf32dadbc81956db69b05d7128c2df80d))

## [2.0.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/2.0.0...2.0.1) (2025-01-03)

### Bug Fixes

* Finished testing ([388c933](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/388c933e804cc133428158ac57d11f165f706a2c))

## [2.0.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/1.1.1...2.0.0) (2025-01-03)

### ⚠ BREAKING CHANGES

* testing

See merge request dp/de/products/builder/builder-ui!490

* Merge branch 'feat--TestingVersioning' into 'main' ([ddc760d](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ddc760d598501c7242ca206e36829d415daa2dc5))

## [1.1.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/1.1.0...1.1.1) (2025-01-03)

### Bug Fixes

* Update footer.component.ts ([96a6136](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/96a61365ed83fbadcb46885f2a972acb444bb377))

## [1.1.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/1.0.3...1.1.0) (2025-01-03)

### Bug Fixes

* testing ([7608a93](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/7608a939aa78939750746207334e31d723ebd70b))

### Features

* testing ([29f7117](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/29f711788dea9f1795cf7d6302cc8198104deeaa))

## [1.0.3](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/1.0.2...1.0.3) (2025-01-02)

### Bug Fixes

* bp details bug ([609b5c4](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/609b5c49938140eb80fdb9d61c861d8fc2f0b258))

## [1.0.2](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/1.0.1...1.0.2) (2025-01-02)

### Bug Fixes

* bff package update ([aa3d863](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/aa3d863d67f7688a78e16dfa874c2d5fd71772d0))
* updating packages ([dc96a7c](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/dc96a7c3dd772c7aca80c5eb736cd1d14bf68e58))

## [1.0.1](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/1.0.0...1.0.1) (2025-01-01)

### Bug Fixes

* Addding comment to test semantic versiong process ([72f671c](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/72f671c68426056df18d4a91480bc2edcce320a5))

## [1.0.0](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/compare/...1.0.0) (2025-01-01)

### Bug Fixes

* adding semantic versioning ([53347d9](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/53347d9d517c35bab49a5b6a565713d99930e56a))
* Correct the version in version.txt file ([2f2d6d6](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2f2d6d6af14544e645ee61d9d585a26590972d99))
* releaserc file to remove the extra branch ([4060625](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/406062527e4ec59e664458f55dcbb03509b6ca21))
* semantic version test ([ee74623](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/ee74623b3b42b13b47db763a5c48797c680e193b))
* testing updated pipeline rules ([e0a5c1c](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/e0a5c1c5633069bcd2717085ab6a3a6023e3afa7))
* Update .releaserc.yaml ([2aaa0ff](https://gitlab.in.gfk.com/dp/de/products/builder/builder-ui/commit/2aaa0ffebfb0ecbc27e2fa2a450fd68036aa60b4))
