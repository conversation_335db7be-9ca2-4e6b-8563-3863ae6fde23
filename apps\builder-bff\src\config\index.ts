import { Config, DomainProxy, UrmOptions } from '@gfk/bff';
import { version } from '../../../../package.json';
interface extendedConfig extends Config {
    userRoleManagement: UrmOptions,
    opa: UrmOptions
}
const config: extendedConfig = {
    environment: 'local',
    cookie: {
        keys: ['CAT KEYBOARD CATERPILLAR'],
        // maxAge: 1000
    },
    proxy: [
        new DomainProxy({
            domain: 'https://projectservices-api.t1.dwh.in.gfk.com',
            // domain: 'https://localhost:5002',
            route: '/bx',
        }),
        new DomainProxy({
            domain: 'https://date-api-shared.t1.dwh.in.gfk.com',
            route: '/date-api',
        }),
        new DomainProxy({
            domain: 'https://country-api-shared.t1.dwh.in.gfk.com',
            route: '/country-api',
        }),
        new DomainProxy({
            domain: 'https://loadmonitor-api.t1.dwh.in.gfk.com',
            route: '/lmx-api',
        }),
        new DomainProxy({
            domain: 'https://userroleassignment-api.t1.dwh.in.gfk.com',
            route: '/user-role',
        }),
        new DomainProxy({
            // domain: 'https://urmapi-nke.in.gfk.com',
            domain: 'https://urmapi-t1.in.gfk.com',
            route: '/urm-api',
        }),
        new DomainProxy({
            domain: 'https://projectservicessecurity-api.t1.dwh.in.gfk.com',
            route: '/projectservice-security',
        }),
        new DomainProxy({
            domain: 'https://administrator-api.t1.dwh.in.gfk.com',
            route: '/base-channel-rearrangement',
        }),
        new DomainProxy({
            domain: 'https://infoshare-api.t1.dwh.in.gfk.com',
            route: '/infoshare',
        }),
        new DomainProxy({
            domain: 'https://productgroup-api.t1.dwh.in.gfk.com',
            route: '/product-group',
        }),
    ],
    identityServer: {
        codeFlow: {
            knownAuthorities: ['https://GfkDataPlatform.b2clogin.com'],
            clientId: 'aad3fe18-d5ef-4d37-964f-554e2c14722a', // TBD: Has to be adapted in ENV
            clientSecret: '****************************************', // TBD: Has to be adapted in ENV
            authority: 'https://GfkDataPlatform.b2clogin.com/GfkDataPlatform.onmicrosoft.com/B2C_1A_SUSI_INTERNAL',
            redirectUri: 'http://localhost:4200/redirect-web',
            scopes: [`openid`, 'offline_access', 'https://GfkDataPlatform.onmicrosoft.com/urm/Users.Me'],
        },
        clientCredentialsFlow: {
            clientId: 'aad3fe18-d5ef-4d37-964f-554e2c14722a', // TBD: Has to be adapted in ENV
            clientSecret: '****************************************', // TBD: Has to be adapted in ENV
            authority: 'https://GfkDataPlatform.b2clogin.com/GfkDataPlatform.onmicrosoft.com/B2C_1A_SUSI_INTERNAL',
            apps: [
                {
                    domain: 'projectservices-api.t1.dwh.in.gfk.com',
                                // domain: 'http://localhost:5002',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/dp.dwh.bx.api/.default'],
                },
                {
                    domain: 'date-api-shared.t1.dwh.in.gfk.com',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/dwh.shared.date/.default'],
                },
                {
                    domain: 'country-api-shared.t1.dwh.in.gfk.com',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/dp.dwh.bx.api/.default'],
                },
                {
                    domain: 'loadmonitor-api.t1.dwh.in.gfk.com',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/dwh.lmx/.default'],
                },
                {
                    domain: 'userroleassignment-api.t1.dwh.in.gfk.com',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/dp.dwh.bx.api/.default'],
                },
                {
                    // domain: 'urmapi-nke.in.gfk.com',
                    domain: 'urmapi-t1.in.gfk.com',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/urm/.default'],
                },
                {
                    domain: 'projectservicessecurity-api.t1.dwh.in.gfk.com',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/dp.dwh.bx.api/.default'],
                },
                {
                    domain: 'administrator-api.t1.dwh.in.gfk.com',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/dp.dwh.administrator.api/.default'],
                },
                {
                    domain: 'infoshare-api.t1.dwh.in.gfk.com',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/dp.dwh.bx.api/.default'],
                },
                {
                    domain: 'productgroup-api.t1.dwh.in.gfk.com',
                    scopes: ['https://GfkDataPlatform.onmicrosoft.com/dp.dwh.bx.api/.default'],
                },
            ]
        },
    },
    urm: {
        // baseUri: 'https://urmapi-nke.in.gfk.com',
        baseUri: 'https://urmapi-t1.in.gfk.com',
        userEndpoint: '/api/v1/users/me',
        roleEndpoint: '/api/v1/roles/me',
        roleFilter: ['*I_dont_have_access*'],
        disabled: false
    },
    userRoleManagement: {
        baseUri: 'https://userroleassignment-api.t1.dwh.in.gfk.com',
        userEndpoint: '',
        roleEndpoint: '/api/userroles/userrole',
        roleFilter: [],
        disabled: false
    },
    opa: {
        baseUri: 'https://opastage.shared.in.gfk.com/v1/data/authzt1/allow_access',
        userEndpoint: '',
        roleEndpoint: '',
        roleFilter: [],
        disabled: false
    },
    exception: {
        showDetails: true,
    },
    health: {
        name: 'builder-bff',
        version,
    },
    tracing: {
        namespace: 'local',
        otelCollectorUrl: 'https://frontend.collector.tracing.vpc1448.in.gfk.com:443/v1/traces',
        serviceName: 'DP.DWH.BX',
        version,
        customResourceAttributes: {
            developer: 'BX',
        },
    },
    metric: { disabled:true,route:"metrics",app:"BFF Example APP",prefix:"bff_app_"}
};
export default config;