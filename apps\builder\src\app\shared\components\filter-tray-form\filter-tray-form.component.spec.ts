import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FilterTrayFormComponent, AutocompleteOption } from './filter-tray-form.component';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { GetApiService } from '@builder/shared/services/get-api.service';
import { PostApiService } from '@builder/shared/services/post-api.service';
import { EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core'
import { of, Subject, throwError } from 'rxjs';

describe('FilterTrayFormComponent', () => {
  let component: FilterTrayFormComponent;
  let fixture: ComponentFixture<FilterTrayFormComponent>;
  let getApiServiceMock: any;
  let postApiServiceMock: any;
  let routerMock: any;
  let notificationServiceMock: any;

  beforeEach(async () => {
    getApiServiceMock = {
      getUserRoleSubject: jest.fn().mockReturnValue(new Subject<any>()),
      getlmxApiSectors: jest.fn().mockReturnValue(of([])),
      getAsyncDataType: jest.fn().mockReturnValue(of([])),
      getAsyncPurpose: jest.fn().mockReturnValue(of([])),
      getAsyncUserEmails: jest.fn().mockReturnValue(of([])),
      getAsyncUserStatus: jest.fn().mockReturnValue(of([])),
    };
    postApiServiceMock = {
      getlmxApiCategories: jest.fn().mockReturnValue(of([])),
      getAsyncProductGroup: jest.fn().mockReturnValue(of([])),
    };
    routerMock = { url: '/some-url' };
    notificationServiceMock = {
      showNotification: jest.fn()
    };

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule],
      declarations: [FilterTrayFormComponent],
      providers: [
        FormBuilder,
        { provide: GetApiService, useValue: getApiServiceMock },
        { provide: PostApiService, useValue: postApiServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: EdsNotificationService, useValue: notificationServiceMock },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FilterTrayFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form', () => {
    expect(component.filterFG).toBeTruthy();
    expect(component.filterFG.controls['baseProjectPanel']).toBeTruthy();
  });

  it('should emit selectedFilters when form value changes', () => {
    const emitSpy = jest.spyOn(component.selectedFilters, 'emit');
    
    component.filterFG.controls['baseProjectPanel'].setValue(['Panel1']);
    
    expect(emitSpy).toHaveBeenCalledWith(expect.any(Object));
  });

  it('should call getSectorList and getCategory on init if not in user-role-list URL', () => {
    routerMock.url = '/some-url';
    fixture = TestBed.createComponent(FilterTrayFormComponent);
    component = fixture.componentInstance;

    const getSectorListSpy = jest.spyOn(component, 'getSectorList');
    const getCategorySpy = jest.spyOn(component, 'getCategory');

    component.ngOnInit();

    expect(getSectorListSpy);
    expect(getCategorySpy);
  });

  it('should call getUserEmails and getUserStatus on init if in user-role-list URL', () => {
    routerMock.url = '/some-url';
    fixture = TestBed.createComponent(FilterTrayFormComponent);
    component = fixture.componentInstance;

    const getUserEmailsSpy = jest.spyOn(component, 'getUserEmails');
    const getUserStatusSpy = jest.spyOn(component, 'getUserStatus');

    component.ngOnInit();

    expect(getUserEmailsSpy);
    expect(getUserStatusSpy);
  });

  it('should subscribe to getUserRoleSubject if not in user-role-list URL', () => {
    const userRoleSubject = new Subject<any>();
    getApiServiceMock.getUserRoleSubject.mockReturnValue(userRoleSubject.asObservable());

    component.ngOnInit();

    userRoleSubject.next({ name: 'TestRole' });

    expect(getApiServiceMock.getUserRoleSubject).toHaveBeenCalled();
  });

  it('should call getProductGroup', () => {
    jest.spyOn(component, 'getProductGroup');
    expect(component.getProductGroup);
  });

  it('should call notifyWidget on error', () => {
    postApiServiceMock.getAsyncProductGroup.mockReturnValue(throwError({ status: 404 }));
    expect(notificationServiceMock.errorAlert);
  });


  it('should unsubscribe from all subscriptions on destroy', () => {
    const subscriptionUnsubscribeSpy = jest.spyOn(component.subscription!, 'unsubscribe');
    const userRoleSubscriptionUnsubscribeSpy = jest.spyOn(component.userRoleSubscription!, 'unsubscribe');

    component.ngOnDestroy();

    expect(subscriptionUnsubscribeSpy).toHaveBeenCalled();
    expect(userRoleSubscriptionUnsubscribeSpy).toHaveBeenCalled();
  });

  it('should notify on error with correct message', () => {
		const testMessage = 'Detailed error description'; // Add a specific message for clarity
		component.notifyWidget('Test error message', 'error', testMessage);
	
		expect(notificationServiceMock.showNotification).toHaveBeenCalledWith('Test error message', {
			message: testMessage,
			duration: 15000,
			variant: NotificationVariant.ERROR,
		});
	});
	
	it('should call showSuccessAlert on successful notify', () => {
		const testMessage = 'Detailed success description'; // Add a specific message for clarity
		component.notifyWidget('Test success message', 'success', testMessage);
	
		expect(notificationServiceMock.showNotification).toHaveBeenCalledWith('Test success message', {
			message: testMessage,
			duration: 15000,
			variant: NotificationVariant.SUCCESS,
		});
	});
});
